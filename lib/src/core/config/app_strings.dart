// App General
const String appName = "NeoRevv";
const String appDescription =
    "A powerful tool to manage agents, track commissions, and streamline real estate operations.";
const String appDescriptionP1 = 'A ';
const String appDescriptionP2 = 'powerful tool ';
const String appDescriptionP3 =
    'to manage agents, track commissions, and streamline real estate operations.';
const String filterResultBannerText = 'Showing results for: ';

// haeder
const String dashboardHead = 'NeoRevv Dashboard';
const String notifications = 'Notifications';
const String settings = 'Settings';

// Login Screen
const String loginTitle = "Login";
const String signInWithGmail = "Sign in with Gmail";
const String signInWithApple = "Sign in with <PERSON>";
const String emailHint = "Enter your email";
const String passwordHint = "Enter your password";
const String rememberMe = "Remember me";
const String forgotPassword = "Forgot password?";
const String loginButton = "Login";
const String loggingIn = "Logging in...";
const String dontHaveAccount = "Don't you have an account? ";
const String signUp = "Sign up";
const String orContinueWith = "Or with email";

// Dashboard
const String brokersTitle = "Brokerages";
const String searchHint = "Search";
const String brokerColumnHeader = "Brokerage Name";
const String contactsColumnHeader = "Contacts";
const String emailAddressColumnHeader = "Email Address";
const String totalAgentsColumnHeader = "Total Agents";
const String joinDateColumnHeader = "Join Date";
const String totalSalesColumnHeader = "Total Sales";
const String actionsColumnHeader = "Actions";
const String viewDocumentsLabel = "View documents";
const String showingDataLabelP1 = "Showing data";
const String showingDataLabelP2 = "entries";
const String toLabel = "to";
const String ofLabel = "of";

const String dashboardTab = "Dashboard";
const String brokersTab = "Brokerages";
const String agentsTab = "Agents";
const String salesTab = "Sales";
const String commissionTab = "Commission";
const String reportsTab = "Reports";

const String addNewButton = "Add New";
const String welcomeLabel = 'Welcome, ';
const String platformOwnerLabel = 'Platform Owner';
const String copyright = 'Copyright © 2025 NeoRevv';
const String homeFooterLabel = 'Home';
const String privacyPolicy = 'Privacy Policy';
const String termsAndConditions = 'Terms and conditions';
const String viewDocuments = "View documents";
const String viewAgents = "View Agents";
const String relatedBrokerLabel = "Related Brokerage";
const String revenueEarnedLabel = "Revenue Earned";

// Sales by Brokers Card
const String salesByBrokers = 'Sales by Brokerages';
const String selectMonthlabel = 'Select Month';
const String sales = 'Sales';
const String viewMore = 'View More';
const String viewLess = 'View Less';
const String filterBy = 'Filter by';
const String joiningDate = 'Joining Date';
const String showingResultsFor = 'Showing Results for ';
const String date = 'Date';
const String topPerformers = 'Top Performers';
const String viewAllBrokers = "view all brokerages";

const String grossCommission = "Gross Commission";
const String monthLabel = "Month";

//Sales Screen Table strings
const String salesTransactionIdColumnHeader = 'Transaction ID';
const String salesAgentColumnHeader = 'Agent';
const String salesPropertyTypeColumnHeader = 'Property Type';
const String salesPropertyAddressColumnHeader = 'Property Address';
const String salesPropertyValueColumnHeader = 'Property Value';
const String salesBuyerNameColumnHeader = 'Buyer Name';
const String salesBuyerAddressColumnHeader = 'Buyer Address';
const String salesListingDateColumnHeader = 'Listing Date';
const String salesDateColumnHeader = 'Sale Date';
const String salesAmountColumnHeader = 'Sales Price';
const String salesCommissionColumnHeader = 'Commission';
const String salesCommissionAmtColumnHeader = ' Commission Amt';
const String salesFormattedHeaders = 'formattedHeaders';
const String salesDataKey = 'salesData';
const String salesPropertyValueKey = 'propertyValue';
const String salesSalePriceKey = 'salePrice';
const String salesCommissionKey = 'commission';
const String salesCommissionAmtKey = 'commissionAmt';
const String salesDoc = 'Preview Closing Document';

//Broker Listing Page
const String brokersDataKey = 'brokersData';
const String totalBrokerSalesRevenueKey = 'totalSalesRevenue';
const String totalBrokerCommissionKey = 'totalCommission';

const String brokerListNameColumnHeader = 'Brokerage Name';
const String brokerListContactColumnHeader = 'Contacts';
const String brokerListEmailColumnHeader = 'Email Address';
const String brokerListAddressColumnHeader = 'Address';
const String brokerListJoinDateColumnHeader = 'Join Date';
const String brokerListAgentsColumnHeader = 'Total Agents';
const String brokerListSalesColumnHeader = 'Total Sales Volume';
const String brokerListTotalSalesColumnHeader = 'Total Sales';

const String brokerListTotalAgentsKey = 'totalAgents';
const String brokerListtotalSalesVolume = 'totalSalesVolume';
const String brokerListTotalCommissionKey = 'totalCommission';
const String brokerListTotalSalesKey = 'totalSales';
const String brokerListJoinDateKey = 'joinDate';
const String brokerListImageUrlKey = 'imageUrl';
const String brokerListAgentsKey = 'totalAgents';
const String brokerListAddressKey = 'address';
const String brokerListIdKey = 'id';
const String brokerListNameKey = 'name';
const String brokerListContactKey = 'contact';
const String brokerListEmailKey = 'email';
// Agent
const String agents = "Agents";
const String agentName = "Agent Name";
const String agentContact = "Contacts";
const String agentEmail = "Email Address";
const String agentJoinDate = "Join Date";
const String agentSoldHomes = "Sold Home";
const String agentState = "State";
const String agentCity = "City";
const String agentLevel = "Level";
const String agentRole = "Role";
const String agentTotalDeals = "Total Deals";
const String agentEarning = "Earning";
const String agentStatus = "Status";
const String actions = "Actions";
const String filter = "Filter";
const String active = "Active";
const String inactive = "Inactive";
const String searchAgent = "Search";
const String selectAgent = "Select Agent";
const String selectLevel = "Select Level";
const String selectStatus = "Select Status";
const String applyLabel = "Apply";
const String agentFilter = "Agent";
const String filterResult = "Showing Results for ";
const String agentDataKey = "agentData";
const String agentRefferedBy = "Referred By";
const String agentTotalSales = "Total Sales";
const String agentCommission = "Commission";

// Agent Screen Messages
const String pleaseLoginToAccessAgentData =
    "Please log in to access agent data";
const String authenticationRequired = "Authentication Required";
const String errorLoadingData = "Error Loading Data";
const String retry = "Retry";
const String actionClickedFor = "Action clicked for";

// Agent Role Constants
const String agentRoleValue = "AGENT";

// HTTP Status and Error Constants
const String unauthorizedStatus = "401";
const String unauthorizedText = "Unauthorized";

// Formatting Constants
const String currencySymbol = "\$";
const String errorPrefix = "Error: ";

// Agent Network Hierarchy
const String agentNetworkScreen = "Agent Network";
const String agentHierarchy = "Agent Hierarchy";
const String agentNetwork = "Agent Network";
const String totalSalesRevenue = "Total Sales Revenue";
const String totalCommission = "Total Commission";
const String recruitsCount = "Recruits Count ";
const String recruitsMobile = "Recruits ";
const String viewProfile = "View Profile";
const String agentsRecruitedBy = "Agents recruited by";
const String brokerLabel = "Brokerage";
const String agentLabel = "Agent";
const String officeStaffLabel = "Office Staff";
const String noRecruitsFound = "No recruits found";
const String hasNotRecruitedAnyAgentsYet = "hasn't recruited any agents yet.";
const String noBrokerSelected = "No brokerage selected";

// Breadcrumb
const String dashboardAdmin = 'Dashboard Admin';
const String addNewBroker = 'Add New Brokerage';
const String reviewClosingDocument = 'Review Closing Document';
const String editClosingDocument = 'Edit Closing Document';

// Broker Registration Screen
const String brokerInformation = 'Brokerage Information';
const String firstName = 'First Name';
const String lastName = 'Last Name';
const String phone = 'Phone';
const String email = 'Email';
const String company = 'Company';
const String city = 'City';
const String stateProvince = 'State/Province';
const String postalZipCode = 'Postal/Zip Code';
const String country = 'Country';
const String enterFirstName = 'Enter your first name';
const String enterLastName = 'Enter your last name';
const String enterPhone = 'Enter your phone number';
const String enterEmail = 'Enter your email address';
const String enterCompany = 'Enter your company name';
const String uploadDocuments = 'Upload Documents';
const String eoInsuranceCertificate =
    'E&O Insurance Certificate (Errors and Omissions)';
const String brokerageLicense = 'Brokerage License';
const String principalBrokerId = 'Principal Brokerage ID';
const String logo = 'Logo';
const String chooseFileOrDragDrop = 'Choose a file or drag & drop it here.';
const String pdfOrImageOnly = 'PDF or image formats only. Max 20 MB.';
const String chooseImageOrDragDrop = 'Choose image or drag & drop it here.';
const String imageFormatsOnly = 'JPG, JPEG, PNG and WEBP. Max 20 MB.';
const String clear = 'Clear';
const String register = 'Register';
const String inviteBroker = 'Invite Brokerage';
const String registerBroker = 'Register Brokerage';
const String registerAgent = 'Register Agent';
const String inviteAgent = 'Invite Agent';
const String agentInformation = 'Agent Information';
const String agentLicenseId = 'Agent License ID';
const String enterAgentLicenseId = 'Enter your License Number';
const String uploadAgentLicenseId = 'Upload Agent License ID';
const String additionalInformation = 'Additional Information';
const String upload = 'Upload';

const String switchTab = 'Switch Tab';
const String switchTabConfirmation =
    'Are you sure you want to switch tabs? All the entered data in the form will be lost.';
const String clearData = 'Clear Data';
const String clearDataConfirmation =
    'Are you sure you want to clear the form?\nAll entered data will be lost, and you will need to re-enter it.';
const String cancel = 'Cancel';
const String ok = 'OK';
const String processingData = 'Processing Data...';

// Validation
const String thisFieldIsRequired = 'This field is required';
const String phoneNumberIsRequired = 'Phone number is required';
const String emailIsRequired = 'Email is required';
const String invalidEmail = 'Please enter a valid email address';
const String invalidPhone = 'Please enter a valid 10-digit phone number';
const String pleaseFillRequiredFields =
    'Please fill all required fields correctly';
const String passwordIsRequired = 'Password is required';
const String passwordMustBeAtLeast6Characters =
    'Password must be at least 6 characters';
const String passwordsDoNotMatch = 'Passwords do not match';
const String pleaseUploadLicence = 'Please upload the license file.';

// Edit Closing Document

const String representing = "Representing:";
const String buyer = "Buyer";
const String seller = "Seller";
const String transactionId = "Transaction ID";
const String transactionName = "Transaction Name";
const String address = "Address";
const String salesVolume = "Sales Volume";
const String moneyReceived = "Money Received(Deposit)";
const String dateDeposited = "Date Deposited";
const String saleType = "Sale Type:";
const String traditional = "Traditional";
const String lease = "Lease";
const String commercial = "Commercial";
const String commissionSplit = "Commission Split";
const String dateReleased = "Date Released";
const String listingDate = "Listing Date";
const String expirationDate = "Expiration Date(of Listing)";
const String closingDate = "Closing Date";
const String legalDescription = "Legal Description";
const String escrowNumber = "Escrow Number";
const String dateReceived = "Date Received";
const String amountReleased = "Amount Released";
const String representedContact = "Represented Contact";

const String companyPhoneNumber = "Company Phone Number";
const String contactAddress = "Contact Address";
const String leadSource = "Lead Source";
const String uploadBrokerSignature = "Upload Brokerage Signature";
const String save = "Save";
const String uploadText = "Choose a file or drag and drop it here.";
const String uploadFormat = "Image formats only. Max 20 MB";
const String submit = "Submit";
const String or = "Or";
const String saleAgreement = "Sale Agreement";
const String closingDocument = "Closing Document";

const String salesAgreement = 'Sales Agreement';
const String documentFileName = 'The document file.pdf';
const String view = 'View';
const String download = 'Download';
const String edit = 'Edit';
const String uploadSignature = 'Upload Signature';
const String saleTypeLabel = 'Sale Type:';
const String reviewerDetails = 'Reviewer Details';

const String commissionContent = "Commission Content";
const String reportsContent = "Reports Content";
//calender strings
const String quickSelection = 'Quick Selection';
const String selectDateRange = 'Select date range';
const String selectEndDate = 'Select end date';
const String selectStartDate = 'Select start date';

///
/// Errors/ Exceptions
///
const String invalidCredentials = "Invalid username or password";
const String loginFailed = "Login failed";
const String failedToFetchUserProfile = "Failed to fetch user profile";
const String userNotFound = "User not found";
const String unexpectedError = "An unexpected error occurred";
const String noDataAvailable = 'No data available';
const String invalidRefreshToken = 'Invalid refresh token';
const String failedToRefreshToken = 'Failed to refresh token';
const String badRequest = 'Bad request';
const String failedToFetchBrokers = 'Failed to fetch brokerages';
const String failedToFetchAgentDetails = 'Failed to fetch agent details';
const String failedToFetchAgents = 'Failed to fetch agents';
const String failedToFetchBrokerageTopPerformers =
    "Failed to fetch brokerage top performers";
const String failedToFetchAgentTopPerformers =
    "Failed to fetch agent top performers";
const String errorPickingFile = 'Error picking file';

/// Creating agent
const String agentCreatedSuccessfully = 'Agent created successfully!';
const String fileUploadFailed = 'File upload failed:';
const String invalidFile = 'Invalid file: Unable to read file data';
const String agentCreatedSuccessfullyWithUploadPrompt =
    'Agent created successfully! Please upload the license file.';
