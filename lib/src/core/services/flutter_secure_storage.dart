import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '/src/domain/models/login.dart';

class SessionManager {
  final _storage = const FlutterSecureStorage();

  // Save session data
  Future<void> saveSession(LoginModel loginModel) async {
    await _storage.write(key: 'authToken', value: loginModel.jwt);
    await _storage.write(key: 'refreshToken', value: loginModel.refreshToken);
  }

  // Get token
  Future<String?> getToken() async {
    return await _storage.read(key: 'authToken');
  }

  Future<String?> getRefreshToken() async {
    return await _storage.read(key: 'refreshToken');
  }

  // Check if logged in
  Future<bool> isLoggedIn() async {
    return await getToken() != null;
  }

  // Clear session
  Future<void> clearSession() async {
    await _storage.delete(key: 'authToken');
    await _storage.delete(key: 'refreshToken');
  }
}