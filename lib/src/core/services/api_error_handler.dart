import 'package:dio/dio.dart';
import '../config/app_strings.dart';
import '../services/exceptions.dart';

class ApiErrorHandler {
  static Exception handleResponseError(int? statusCode, dynamic data) {
    final message = data['message'];
    switch (statusCode) {
      case 401:
        return InvalidCredentialsException(
          message: message ?? invalidCredentials,
          statusCode: 401,
        );
      case 404:
        return ApiException(message: message ?? badRequest, statusCode: 404);
      default:
        return ApiException(
          message: message ?? failedToFetchUserProfile,
          statusCode: statusCode ?? 500,
        );
    }
  }

  static Exception handleDioException(DioException e, String defaultError) {
    final statusCode = e.response?.statusCode;
    final message = e.response?.data['message'] ?? defaultError;

    return handleResponseError(statusCode, {'message': message});
  }
}
