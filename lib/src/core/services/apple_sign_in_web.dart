import 'apple_sign_in_service.dart';

class AppleSignInServiceWeb implements AppleSignInService {
  @override
  Future<bool> isAvailable() async {
    // Apple Sign In is not available on web
    return false;
  }

  @override
  Future<AppleSignInResult?> signIn() async {
    throw UnsupportedError('Apple Sign In is not supported on web');
  }
}

AppleSignInService getAppleSignInService() => AppleSignInServiceWeb();
