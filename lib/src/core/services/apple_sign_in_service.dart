import 'package:flutter/foundation.dart';

// Conditional import based on platform
import 'apple_sign_in_stub.dart'
    if (dart.library.io) 'apple_sign_in_native.dart'
    if (dart.library.html) 'apple_sign_in_web.dart';

abstract class AppleSignInService {
  static AppleSignInService get instance => getAppleSignInService();
  
  Future<bool> isAvailable();
  Future<AppleSignInResult?> signIn();
}

class AppleSignInResult {
  final String userIdentifier;
  final String? email;
  final String? givenName;
  final String? familyName;

  AppleSignInResult({
    required this.userIdentifier,
    this.email,
    this.givenName,
    this.familyName,
  });
}
