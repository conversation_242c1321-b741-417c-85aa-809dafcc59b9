import 'package:dio/dio.dart';
import '../../core/network/dio_client.dart';
import '../../core/services/api_error_handler.dart';
import '../../core/network/api_config.dart';
import '../../core/config/app_strings.dart';
import '../../core/services/exceptions.dart';
import '../../domain/models/user.dart';
import '../../domain/repository/user_repository.dart';

class UserRepositoryImpl extends UserRepository {
  UserRepositoryImpl();

  static const String baseUrl = APIConfig.baseUrl;
  static const String userProfileUrl = APIConfig.userProfile;

  @override
  Future<User> getUserProfile() async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.get(userProfileUrl);

      if (response.statusCode == 200) {
        return User.fromJson(response.data);
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, failedToFetchUserProfile);
    } catch (e) {
      throw ApiException(
        message: '$unexpectedError: ${e.toString()}',
        statusCode: 500,
      );
    }
  }
}
