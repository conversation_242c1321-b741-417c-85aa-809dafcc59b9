import 'package:dio/dio.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import '../../core/network/dio_client.dart';
import '../../core/services/api_error_handler.dart';
import '../../core/network/api_config.dart';
import '../../core/config/app_strings.dart';
import '../../core/services/exceptions.dart';
import '../../domain/models/agent_model.dart';
import '../../domain/repository/agent_repository.dart';

class AgentRepositoryImpl extends AgentRepository {
  AgentRepositoryImpl();

  static const String baseUrl = APIConfig.baseUrl;
  static const String agentsSearchUrl = APIConfig.agentsSearch;
  static const String agentsCreateUrl = APIConfig.agentCreate;
  static const String loginUrl = APIConfig.login;

  @override
  Future<ApiAgentResponse> getAgents(Map<String, dynamic> requestBody) async {
    try {
      final dio = await DioClient.getDio();

      final response = await dio.post(agentsSearchUrl, data: requestBody);

      if (response.statusCode == 200) {
        return ApiAgentResponse.fromJson(response.data);
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, failedToFetchAgents);
    } catch (e) {
      throw ApiException(
        message: '$unexpectedError: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  @override
  Future<AgentModel> getAgentById(String agentId) async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.get('$agentsSearchUrl/$agentId');

      if (response.statusCode == 200) {
        return AgentModel.fromJson(response.data['data'] ?? response.data);
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, failedToFetchAgentDetails);
    } catch (e) {
      throw ApiException(
        message: '$unexpectedError: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  @override
  Future<dynamic> createAgent(Map<String, dynamic> payload) async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.post(agentsCreateUrl, data: payload);
      if (response.statusCode == 200 || response.statusCode == 201) {
        return response.data; // Return the actual response data
      } else {
        return false;
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, 'Failed to create agent');
    } catch (e) {
      throw ApiException(
        message: '$unexpectedError: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  @override
  Future<bool> uploadAgentFile(Map<String, dynamic> requestBody) async {
    try {
      final dio = await DioClient.getDioForMultipart();

      // Create FormData for multipart upload
      FormData formData = FormData();

      // Add non-file fields
      requestBody.forEach((key, value) {
        if (key != 'file') {
          formData.fields.add(MapEntry(key, value.toString()));
        }
      });

      // Add file if present
      if (requestBody['file'] != null) {
        final file = requestBody['file'];
        if (file is PlatformFile) {
          try {
            MultipartFile multipartFile = await _createMultipartFile(file);

            formData.files.add(MapEntry('file', multipartFile));
          } catch (e) {
            throw ApiException(
              message: 'Failed to process file: ${file.name}',
              statusCode: 400,
            );
          }
        } else {
          throw ApiException(
            message: 'Invalid file type provided',
            statusCode: 400,
          );
        }
      } else {
        throw ApiException(
          message: 'No file provided for upload',
          statusCode: 400,
        );
      }

      final response = await dio.post(
        APIConfig.agentFileUpload,
        data: formData,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return true;
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(
        e,
        'Failed to upload agent file',
      );
    } catch (e) {
      throw ApiException(
        message: '$unexpectedError: ${e.toString()}',
        statusCode: 500,
      );
    }
  }

  /// Helper method to create MultipartFile from PlatformFile
  /// Handles both web (bytes) and mobile/desktop (file path) platforms
  Future<MultipartFile> _createMultipartFile(PlatformFile file) async {
    if (kIsWeb) {
      // For web platform, use bytes
      if (file.bytes != null) {
        return MultipartFile.fromBytes(file.bytes!, filename: file.name);
      } else {
        throw ApiException(
          message: 'File bytes not available for web upload: ${file.name}',
          statusCode: 400,
        );
      }
    } else {
      // For mobile/desktop platforms, use file path
      if (file.path != null) {
        return await MultipartFile.fromFile(file.path!, filename: file.name);
      } else {
        throw ApiException(
          message: 'File path not available for mobile upload: ${file.name}',
          statusCode: 400,
        );
      }
    }
  }
}
