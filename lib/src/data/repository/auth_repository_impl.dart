import '../../core/network/dio_client.dart';
import '../../core/services/api_error_handler.dart';
import '/src/core/config/app_strings.dart';
import '/src/domain/models/login.dart';
import '../../core/network/api_config.dart' show APIConfig;
import '../../domain/repository/auth_repository.dart';
import '../../core/services/exceptions.dart';
import '../../core/network/api_config.dart';
import 'package:dio/dio.dart';

class AuthRepositoryImpl extends AuthRepository {
  AuthRepositoryImpl();

  static final String baseUrl = APIConfig.baseUrl;
  static const String loginUrl = APIConfig.login;

  @override
  Future<dynamic> login(Map<String, dynamic> payload) async {
    try {
      final _dio = await DioClient.getDio();
      final response = await _dio.post(loginUrl, data: payload);
      print('Request Headers Login: ${_dio}');

      print('Request Headers Login: ${_dio.options}');
      print('Request Headers Login: ${_dio.options.headers}');
      print('Request Headers Login: ${_dio.options.baseUrl}');
      print('Request URL Login: ${loginUrl}');
      if (response.statusCode == 200) {
        return LoginModel.fromJson(response.data);
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, loginFailed);
    } catch (e) {
      throw ApiException(message: e.toString(), statusCode: 500);
    }
  }
}
