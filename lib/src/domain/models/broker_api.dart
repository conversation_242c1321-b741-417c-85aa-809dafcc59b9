// To parse this JSON data, do
//
//     final brokerApi = brokerApiFromJson(jsonString);

import 'dart:convert';

BrokerApi brokerApiFromJson(String str) => BrokerApi.fromJson(json.decode(str));

String brokerApiToJson(BrokerApi data) => json.encode(data.toJson());

class BrokerApi {
  List<Brokers> brokers;
  Pageable? pageable;
  int totalElements;
  int totalPages;
  bool last;
  int size;
  int number;
  BrokerSort? sort;
  int numberOfElements;
  bool first;
  bool empty;

  BrokerApi({
    required this.brokers,
    required this.pageable,
    required this.totalElements,
    required this.totalPages,
    required this.last,
    required this.size,
    required this.number,
    required this.sort,
    required this.numberOfElements,
    required this.first,
    required this.empty,
  });

  BrokerApi copyWith({
    List<Brokers>? content,
    Pageable? pageable,
    int? totalElements,
    int? totalPages,
    bool? last,
    int? size,
    int? number,
    BrokerSort? sort,
    int? numberOfElements,
    bool? first,
    bool? empty,
  }) => Broke<PERSON><PERSON><PERSON>(
    brokers: content ?? this.brokers,
    pageable: pageable ?? this.pageable,
    totalElements: totalElements ?? this.totalElements,
    totalPages: totalPages ?? this.totalPages,
    last: last ?? this.last,
    size: size ?? this.size,
    number: number ?? this.number,
    sort: sort ?? this.sort,
    numberOfElements: numberOfElements ?? this.numberOfElements,
    first: first ?? this.first,
    empty: empty ?? this.empty,
  );

  factory BrokerApi.fromJson(Map<String, dynamic> json) => BrokerApi(
    brokers: json["content"] != null
        ? List<Brokers>.from(json["content"].map((x) => Brokers.fromJson(x)))
        : [],
    pageable: Pageable.fromJson(json["pageable"]),
    totalElements: json["totalElements"] ?? 0,
    totalPages: json["totalPages"] ?? 0,
    last: json["last"] ?? false,
    size: json["size"] ?? 0,
    number: json["number"] ?? 0,
    sort: BrokerSort.fromJson(json["sort"]),
    numberOfElements: json["numberOfElements"] ?? 0,
    first: json["first"] ?? false,
    empty: json["empty"] ?? false,
  );

  Map<String, dynamic> toJson() => {
    "content": List<dynamic>.from(brokers.map((x) => x.toJson())),
    "pageable": pageable?.toJson(),
    "totalElements": totalElements,
    "totalPages": totalPages,
    "last": last,
    "size": size,
    "number": number,
    "sort": sort?.toJson(),
    "numberOfElements": numberOfElements,
    "first": first,
    "empty": empty,
  };
}

class Brokers {
  String id;
  String firstName;
  String lastName;
  String fullName;
  String email;
  String phone;
  String role;
  String state;
  String city;
  DateTime createdAt;
  DateTime joiningDate;
  int totalDownlineAgents;
  int totalRecruitedAgents;
  int totalDownlineSales;
  int salesMade;
  int commissionEarnings;
  String? associatedBroker;

  Brokers({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.fullName,
    required this.email,
    required this.phone,
    required this.role,
    required this.state,
    required this.city,
    required this.createdAt,
    required this.joiningDate,
    required this.totalDownlineAgents,
    required this.totalRecruitedAgents,
    required this.totalDownlineSales,
    required this.salesMade,
    required this.commissionEarnings,
    required this.associatedBroker,
  });

  Brokers copyWith({
    String? id,
    String? firstName,
    String? lastName,
    String? fullName,
    String? email,
    String? phone,
    String? role,
    String? state,
    String? city,
    DateTime? createdAt,
    DateTime? joiningDate,
    int? totalDownlineAgents,
    int? totalRecruitedAgents,
    int? totalDownlineSales,
    int? salesMade,
    int? commissionEarnings,
    dynamic associatedBroker,
  }) => Brokers(
    id: id ?? this.id,
    firstName: firstName ?? this.firstName,
    lastName: lastName ?? this.lastName,
    fullName: fullName ?? this.fullName,
    email: email ?? this.email,
    phone: phone ?? this.phone,
    role: role ?? this.role,
    state: state ?? this.state,
    city: city ?? this.city,
    createdAt: createdAt ?? this.createdAt,
    joiningDate: joiningDate ?? this.joiningDate,
    totalDownlineAgents: totalDownlineAgents ?? this.totalDownlineAgents,
    totalRecruitedAgents: totalRecruitedAgents ?? this.totalRecruitedAgents,
    totalDownlineSales: totalDownlineSales ?? this.totalDownlineSales,
    salesMade: salesMade ?? this.salesMade,
    commissionEarnings: commissionEarnings ?? this.commissionEarnings,
    associatedBroker: associatedBroker ?? this.associatedBroker,
  );

  factory Brokers.fromJson(Map<String, dynamic> json) => Brokers(
    id: json["id"] ?? "",
    firstName: json["firstName"] ?? "",
    lastName: json["lastName"] ?? "",
    fullName: json["fullName"] ?? "",
    email: json["email"] ?? "",
    phone: json["phone"] ?? "",
    role: json["role"] ?? "",
    state: json["state"] ?? "",
    city: json["city"] ?? "",
    createdAt: DateTime.parse(json["createdAt"] ?? DateTime.now().toString()),
    joiningDate: DateTime.parse(
      json["joiningDate"] ?? DateTime.now().toString(),
    ),
    totalDownlineAgents: json["totalDownlineAgents"] ?? 0,
    totalRecruitedAgents: json["totalRecruitedAgents"] ?? 0,
    totalDownlineSales: json["totalDownlineSales"] ?? 0,
    salesMade: json["salesMade"] ?? 0,
    commissionEarnings: json["commissionEarnings"] ?? 0,
    associatedBroker: json["associatedBroker"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "firstName": firstName,
    "lastName": lastName,
    "fullName": fullName,
    "email": email,
    "phone": phone,
    "role": role,
    "state": state,
    "city": city,
    "createdAt": createdAt.toIso8601String(),
    "joiningDate":
        "${joiningDate.year.toString().padLeft(4, '0')}-${joiningDate.month.toString().padLeft(2, '0')}-${joiningDate.day.toString().padLeft(2, '0')}",
    "totalDownlineAgents": totalDownlineAgents,
    "totalRecruitedAgents": totalRecruitedAgents,
    "totalDownlineSales": totalDownlineSales,
    "salesMade": salesMade,
    "commissionEarnings": commissionEarnings,
    "associatedBroker": associatedBroker,
  };
}

class Pageable {
  int pageNumber;
  int pageSize;
  BrokerSort? sort;
  int offset;
  bool paged;
  bool unpaged;

  Pageable({
    required this.pageNumber,
    required this.pageSize,
    required this.sort,
    required this.offset,
    required this.paged,
    required this.unpaged,
  });

  Pageable copyWith({
    int? pageNumber,
    int? pageSize,
    BrokerSort? sort,
    int? offset,
    bool? paged,
    bool? unpaged,
  }) => Pageable(
    pageNumber: pageNumber ?? this.pageNumber,
    pageSize: pageSize ?? this.pageSize,
    sort: sort ?? this.sort,
    offset: offset ?? this.offset,
    paged: paged ?? this.paged,
    unpaged: unpaged ?? this.unpaged,
  );

  factory Pageable.fromJson(Map<String, dynamic> json) => Pageable(
    pageNumber: json["pageNumber"] ?? 0,
    pageSize: json["pageSize"] ?? 0,
    sort: BrokerSort.fromJson(json["sort"]),
    offset: json["offset"] ?? 0,
    paged: json["paged"] ?? false,
    unpaged: json["unpaged"] ?? false,
  );

  Map<String, dynamic> toJson() => {
    "pageNumber": pageNumber,
    "pageSize": pageSize,
    "sort": sort?.toJson(),
    "offset": offset,
    "paged": paged,
    "unpaged": unpaged,
  };
}

class BrokerSort {
  bool sorted;
  bool unsorted;
  bool empty;

  BrokerSort({
    required this.sorted,
    required this.unsorted,
    required this.empty,
  });

  BrokerSort copyWith({bool? sorted, bool? unsorted, bool? empty}) =>
      BrokerSort(
        sorted: sorted ?? this.sorted,
        unsorted: unsorted ?? this.unsorted,
        empty: empty ?? this.empty,
      );

  factory BrokerSort.fromJson(Map<String, dynamic> json) => BrokerSort(
    sorted: json["sorted"] ?? false,
    unsorted: json["unsorted"] ?? false,
    empty: json["empty"] ?? false,
  );

  Map<String, dynamic> toJson() => {
    "sorted": sorted,
    "unsorted": unsorted,
    "empty": empty,
  };
}
