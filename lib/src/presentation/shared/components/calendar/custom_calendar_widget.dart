import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:intl/intl.dart';
import 'package:neorevv/src/core/utils/date_formatter.dart';
import '../../../../core/config/app_strings.dart';
import '../../../../core/config/json_consts.dart';
import '/src/core/config/constants.dart';
import '/src/core/config/responsive.dart';
import '/src/core/theme/app_theme.dart';
import '/src/core/theme/app_fonts.dart';
import 'month_year_picker.dart';

class CustomCalendarWidget extends HookWidget {
  final bool fromFilter;
  final DateTime currentMonth;
  final String selectedQuickOption;
  final DateTime? selectedDate;
  final DateTime? rangeStart;
  final DateTime? rangeEnd;
  final bool isCustomRangeMode;
  final DateTime? customRangeStart;
  final DateTime? customRangeEnd;
  final Function(String) onQuickSelection;
  final Function(DateTime) onDateSelection;
  final Function(int) onNavigateMonth;
  final VoidCallback onCancel;
  final VoidCallback onApply;
  final bool Function(DateTime) isDateInSelectedRange;
  final bool Function(DateTime) isRangeStartDate;

  const CustomCalendarWidget({
    super.key,
    required this.currentMonth,
    required this.selectedQuickOption,
    required this.selectedDate,
    required this.rangeStart,
    required this.rangeEnd,
    required this.isCustomRangeMode,
    required this.customRangeStart,
    required this.customRangeEnd,
    required this.onQuickSelection,
    required this.onDateSelection,
    required this.onNavigateMonth,
    required this.onCancel,
    required this.onApply,
    required this.isDateInSelectedRange,
    required this.isRangeStartDate,
    this.fromFilter = false,
  });

  @override
  Widget build(BuildContext context) {
    final showMonthYearPicker = useState(false);

    // Use custom breakpoint for calendar - only small mobile devices use mobile layout
    // Tablets (≥800px) and desktop use the desktop layout
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobileCalendar =
        screenWidth < 800; // Mobile layout only for screens < 800px

    return Stack(
      children: [
        if (isMobileCalendar)
          _buildMobileCalendar(context, showMonthYearPicker)
        else
          _buildDesktopCalendar(context, showMonthYearPicker),
        if (showMonthYearPicker.value)
          Positioned.fill(
            child: Container(
              color: AppTheme.black.withValues(alpha: 0.5),
              child: Center(
                child: MonthYearPicker(
                  initialDate: currentMonth,
                  onDateSelected: (DateTime newDate) {
                    onNavigateMonth(
                      newDate.month -
                          currentMonth.month +
                          (newDate.year - currentMonth.year) * 12,
                    );
                    showMonthYearPicker.value = false;
                  },
                  onCancel: () {
                    showMonthYearPicker.value = false;
                  },
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildMobileCalendar(
    BuildContext context,
    ValueNotifier<bool> showPicker,
  ) {
    return Container(
      width: 380,
      //height: 500,
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.12), // Slight opacity
            blurRadius: 30, // Large blur for soft edges
            spreadRadius: 10, // Optional: increases size of the shadow
            offset: Offset(0, 8), // Centered shadow on all sides
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(defaultPadding * 1.2), // Increased padding
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildMobileCalendarHeader(showPicker),
            _buildMobileQuickSelectionBubbles(),
            _buildMobileWeekdayHeaders(),
            _buildMobileDatesGrid(),
            _buildMobileActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildDesktopCalendar(
    BuildContext context,
    ValueNotifier<bool> showPicker,
  ) {
    return Container(
      width: 450,
      height: 400,
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppTheme.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Expanded(
            child: Row(
              crossAxisAlignment:
                  CrossAxisAlignment.stretch, // Make sidebar fill full height
              children: [
                _buildQuickSelectionSidebar(context),
                Flexible(
                  child: Padding(
                    padding: const EdgeInsets.all(defaultPadding),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        _buildCalendarHeader(showPicker),
                        _buildWeekdayHeaders(context),
                        Flexible(child: _buildDatesGrid(context)),
                        _buildSelectedDateRangeDisplay(context),
                        _buildActionButtons(context),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickSelectionSidebar(BuildContext context) {
    final sidebarWidth = Responsive.isTablet(context) ? 140.0 : 160.0;
    final List<String> options = fromFilter
        ? (quickSelectionOptions.isNotEmpty
              ? [quickSelectionOptions.first]
              : [])
        : quickSelectionOptions;
    return Container(
      width: sidebarWidth,
      decoration: BoxDecoration(
        color: AppTheme.searchbarBg,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(10),
          bottomLeft: Radius.circular(10),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(defaultPadding),
            child: Text(quickSelection, style: AppFonts.semiBoldTextStyle(14)),
          ),
          Expanded(
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: options.length,
              itemBuilder: (context, index) {
                final option = options[index];
                final isSelected = selectedQuickOption == option;
                return _buildQuickOptionListItem(option, isSelected);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickOptionListItem(String option, bool isSelected) {
    return GestureDetector(
      onTap: () => onQuickSelection(option),
      child: Container(
        margin: const EdgeInsets.symmetric(
          horizontal: defaultPadding / 2,
          vertical: 2,
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: defaultPadding,
          vertical: defaultPadding / 2,
        ),
        decoration: BoxDecoration(
          color: isSelected
              ? AppTheme.selectedComboBoxBorder
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          option,
          style: AppFonts.regularTextStyle(
            12,
            color: isSelected ? AppTheme.white : AppTheme.black,
          ),
        ),
      ),
    );
  }

  Widget _buildCalendarHeader(ValueNotifier<bool> showPicker) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        IconButton(
          onPressed: () => onNavigateMonth(-1),
          icon: Icon(Icons.chevron_left, color: AppTheme.black),
        ),
        GestureDetector(
          onTap: () => showPicker.value = true,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                DateFormat('MMMM yyyy').format(currentMonth),
                style: AppFonts.semiBoldTextStyle(16),
              ),
              SizedBox(width: defaultPadding / 4),
              Icon(
                Icons.calendar_month,
                size: 16,
                color: AppTheme.tableDataFont,
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () => onNavigateMonth(1),
          icon: Icon(Icons.chevron_right, color: AppTheme.black),
        ),
      ],
    );
  }

  Widget _buildWeekdayHeaders(BuildContext context) {
    return Row(
      children: weekdayHeaders
          .map(
            (day) => Expanded(
              child: Center(
                child: Text(
                  day,
                  style: AppFonts.semiBoldTextStyle(
                    Responsive.isMobile(context) ? 14 : 12,
                    color: AppTheme.tableDataFont,
                  ),
                ),
              ),
            ),
          )
          .toList(),
    );
  }

  Widget _buildDatesGrid(BuildContext context) {
    final daysInMonth = DateTime(
      currentMonth.year,
      currentMonth.month + 1,
      0,
    ).day;
    final firstWeekday = DateTime(
      currentMonth.year,
      currentMonth.month,
      1,
    ).weekday;
    final now = DateTime.now();

    // Calculate responsive grid dimensions
    final isMobile = Responsive.isMobile(context);
    final spacing = isMobile ? 4.0 : 3.0; // Reduced spacing

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 7,
        childAspectRatio: 1.0,
        mainAxisSpacing: spacing,
        crossAxisSpacing: spacing,
      ),
      itemCount: 42, // 6 weeks * 7 days
      itemBuilder: (context, index) {
        final dayNumber = index - firstWeekday + 2;
        final isValidDay = dayNumber > 0 && dayNumber <= daysInMonth;

        if (!isValidDay) {
          return const SizedBox();
        }

        final date = DateTime(currentMonth.year, currentMonth.month, dayNumber);
        return _buildDateCell(date, now, context);
      },
    );
  }

  Widget _buildDateCell(DateTime date, DateTime now, BuildContext context) {
    final isSelected =
        selectedDate != null &&
        date.year == selectedDate!.year &&
        date.month == selectedDate!.month &&
        date.day == selectedDate!.day;

    final isToday =
        date.year == now.year && date.month == now.month && date.day == now.day;

    final isInRange = isDateInSelectedRange(date);
    final isStartDate = isRangeStartDate(date);
    final isCustomRangeStart =
        customRangeStart != null &&
        date.year == customRangeStart!.year &&
        date.month == customRangeStart!.month &&
        date.day == customRangeStart!.day;
    final isCustomRangeEnd =
        customRangeEnd != null &&
        date.year == customRangeEnd!.year &&
        date.month == customRangeEnd!.month &&
        date.day == customRangeEnd!.day;

    Color? bgColor;
    Color textColor = AppTheme.black;

    if (isSelected || isStartDate || isCustomRangeStart || isCustomRangeEnd) {
      bgColor = AppTheme.selectedComboBoxBorder;
      textColor = AppTheme.white;
    } else if (isInRange) {
      bgColor = AppTheme.searchbarBg;
      textColor = AppTheme.black;
    } else if (isToday) {
      bgColor = AppTheme.selectedComboBoxBorder.withValues(alpha: 0.2);
      textColor = AppTheme.selectedComboBoxBorder;
    }

    return GestureDetector(
      onTap: () => onDateSelection(date),
      child: Container(
        margin: const EdgeInsets.all(2),
        decoration: BoxDecoration(
          color: bgColor,
          borderRadius: BorderRadius.circular(8),
          border: isToday && !isSelected && !isStartDate
              ? Border.all(color: AppTheme.selectedComboBoxBorder, width: 1)
              : null,
        ),
        child: Center(
          child: Text(
            date.day.toString(),
            style:
                AppFonts.regularTextStyle(
                  Responsive.isMobile(context) ? 14 : 12,
                  color: textColor,
                ).copyWith(
                  fontWeight: isSelected || isToday || isStartDate
                      ? FontWeight.bold
                      : FontWeight.normal,
                ),
          ),
        ),
      ),
    );
  }

  Widget _buildSelectedDateRangeDisplay(BuildContext context) {
    final isMobile = Responsive.isMobile(context);

    return Padding(
      padding: EdgeInsets.only(
        top: isMobile ? defaultPadding * 0.3 : defaultPadding * 0.2,
        left: isMobile
            ? defaultPadding / 2
            : 0, // Remove extra padding for desktop
        right: isMobile
            ? defaultPadding / 2
            : 0, // Remove extra padding for desktop
        bottom: isMobile ? defaultPadding * 0.2 : defaultPadding * 0.1,
      ),
      child: _buildSelectedDisplayButton(),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    final isMobile = Responsive.isMobile(context);

    return Padding(
      padding: EdgeInsets.only(
        top: isMobile
            ? defaultPadding * 0.2
            : defaultPadding * 0.4, // Increased top padding for better spacing
        left: isMobile
            ? defaultPadding / 2
            : 0, // Match selected date range display padding
        right: isMobile
            ? defaultPadding / 2
            : 0, // Match selected date range display padding
        bottom: isMobile ? defaultPadding / 2 : defaultPadding,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          _buildCancelButton(),
          SizedBox(width: isMobile ? defaultPadding / 4 : defaultPadding / 4),
          _buildApplyButton(),
        ],
      ),
    );
  }

  Widget _buildSelectedDisplayButton() {
    String displayText = _getDisplayText();
    return Container(
      width: double.infinity, // Ensure full width like mobile
      padding: EdgeInsets.symmetric(
        horizontal: defaultPadding * 0.8,
        vertical: defaultPadding * 0.6,
      ),
      decoration: BoxDecoration(
        color: AppTheme.searchbarBg.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(2),
        border: Border.all(
          color: AppTheme.comboBoxBorder.withValues(alpha: 0.5),
        ),
      ),
      child: Text(
        displayText,
        style: AppFonts.regularTextStyle(
          13, // Slightly larger font like mobile
          color: AppTheme.tableDataFont,
        ),
        textAlign: TextAlign.center,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildCancelButton() {
    return GestureDetector(
      onTap: onCancel,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: defaultPadding,
          vertical: defaultPadding / 2,
        ),
        decoration: BoxDecoration(
          border: Border.all(color: AppTheme.comboBoxBorder),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(cancel, style: AppFonts.regularTextStyle(12)),
      ),
    );
  }

  Widget _buildApplyButton() {
    final bool isActive = _hasUserInteracted();
    return GestureDetector(
      onTap: isActive ? onApply : null, // Only allow tap when active
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: defaultPadding,
          vertical: defaultPadding / 2,
        ),
        decoration: BoxDecoration(
          color: isActive
              ? AppTheme.selectedComboBoxBorder
              : AppTheme.selectedComboBoxBorder.withValues(
                  alpha: 0.5,
                ), // Inactive state with opacity
          border: Border.all(color: AppTheme.comboBoxBorder),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          applyLabel,
          style: AppFonts.regularTextStyle(
            12,
            color: isActive
                ? AppTheme.white
                : AppTheme.white.withValues(
                    alpha: 0.7,
                  ), // Dimmed text when inactive
          ),
        ),
      ),
    );
  }

  String _getDisplayText() {
    if (selectedDate != null) {
      return AppDateFormatter.formatDateMMddyyyy(selectedDate!);
    } else if (isCustomRangeMode) {
      // Enhanced display for custom range mode
      if (customRangeStart != null && customRangeEnd != null) {
        return '${AppDateFormatter.formatDateMMddyyyy(customRangeStart!)}';// - ${AppDateFormatter.formatDateMMddyyyy(customRangeEnd!)}';
      } else if (customRangeStart != null) {
        return '${AppDateFormatter.formatDateMMddyyyy(customRangeStart!)}';// - $selectEndDate';
      } else {
        return selectStartDate;
      }
    } else if (rangeStart != null &&
        rangeEnd != null &&
        selectedQuickOption.isNotEmpty) {
      return '${AppDateFormatter.formatDateMMddyyyy(rangeStart!)}'; // - ${AppDateFormatter.formatDateMMddyyyy(rangeEnd!)}';
    } else {
      return selectDateRange;
    }
  }

  // Helper method to check if user has interacted with calendar
  bool _hasUserInteracted() {
    return selectedDate != null ||
        (rangeStart != null && rangeEnd != null) ||
        customRangeStart != null ||
        selectedQuickOption.isNotEmpty;
  }

  // Mobile methods - compact design
  Widget _buildMobileQuickSelectionBubbles() {
    final List<String> options = fromFilter
        ? (quickSelectionOptions.isNotEmpty
              ? [quickSelectionOptions.first]
              : [])
        : quickSelectionOptions;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: defaultPadding),
      child: Container(
        width: double.infinity, // TEMP: highlight parent container
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title Padding
            Padding(
              padding: const EdgeInsets.symmetric(
                vertical: defaultPadding * 0.9,
              ),
              child: Text(
                quickSelection,
                style: AppFonts.semiBoldTextStyle(14),
              ),
            ),
            // Horizontal Bubbles (left-aligned)
            SizedBox(
              height: 44,
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                physics: const BouncingScrollPhysics(),
                child: Wrap(
                  alignment: WrapAlignment.start,
                  crossAxisAlignment: WrapCrossAlignment.center,
                  spacing: defaultPadding * 0.4,
                  children: List.generate(options.length, (index) {
                    final option = options[index];
                    final isSelected = selectedQuickOption == option;
                    return _buildMobileQuickOptionBubble(option, isSelected);
                  }),
                ),
              ),
            ),
            SizedBox(height: defaultPadding * 0.6),
          ],
        ),
      ),
    );
  }

  Widget _buildMobileQuickOptionBubble(String option, bool isSelected) {
    return GestureDetector(
      onTap: () => onQuickSelection(option),
      child: Container(
        alignment: Alignment.centerLeft,
        padding: EdgeInsets.symmetric(
          horizontal: defaultPadding * 0.8,
          vertical: defaultPadding * 0.4,
        ),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.selectedComboBoxBorder : AppTheme.white,
          borderRadius: BorderRadius.circular(20), // Bubble style
          border: Border.all(
            color: isSelected
                ? AppTheme.selectedComboBoxBorder
                : AppTheme.comboBoxBorder,
          ),
        ),
        child: Text(
          option,
          style: AppFonts.regularTextStyle(
            12, // Increased font size
            color: isSelected ? AppTheme.white : AppTheme.black,
          ),
        ),
      ),
    );
  }

  Widget _buildMobileCalendarHeader(ValueNotifier<bool> showPicker) {
    return Padding(
      padding: EdgeInsets.only(bottom: defaultPadding * 0.4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          GestureDetector(
            onTap: () => onNavigateMonth(-1),
            child: Container(
              padding: EdgeInsets.all(defaultPadding * 0.6),
              decoration: BoxDecoration(
                color: AppTheme.searchbarBg,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                Icons.chevron_left,
                size: 14,
                color: AppTheme.black.withValues(alpha: 0.7),
              ),
            ),
          ),
          GestureDetector(
            onTap: () => showPicker.value = true,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  AppDateFormatter.formatCalendarHeader(currentMonth),
                  style: AppFonts.semiBoldTextStyle(16), // Increased font size
                ),
                SizedBox(width: defaultPadding * 0.2),
                Icon(
                  Icons.calendar_today,
                  size: 14,
                  color: AppTheme.black.withValues(alpha: 0.7),
                ),
              ],
            ),
          ),
          GestureDetector(
            onTap: () => onNavigateMonth(1),
            child: Container(
              padding: EdgeInsets.all(defaultPadding * 0.6),
              decoration: BoxDecoration(
                color: AppTheme.searchbarBg,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                Icons.chevron_right,
                size: 14,
                color: AppTheme.black.withValues(alpha: 0.7),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMobileWeekdayHeaders() {
    return Padding(
      padding: EdgeInsets.only(bottom: defaultPadding * 0.4),
      child: Row(
        children: weekdayHeaders
            .map(
              (day) => Expanded(
                child: Center(
                  child: Text(
                    day,
                    style: AppFonts.semiBoldTextStyle(
                      12, // Increased font size
                      color: AppTheme.tableDataFont,
                    ),
                  ),
                ),
              ),
            )
            .toList(),
      ),
    );
  }

  Widget _buildMobileDatesGrid() {
    final daysInMonth = DateTime(
      currentMonth.year,
      currentMonth.month + 1,
      0,
    ).day;
    final firstWeekday = DateTime(
      currentMonth.year,
      currentMonth.month,
      1,
    ).weekday;
    final now = DateTime.now();

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 7,
        childAspectRatio: 1.0,
        mainAxisSpacing: 2.0, // Reduced spacing for better fit
        crossAxisSpacing: 2.0, // Reduced spacing for better fit
      ),
      itemCount: 42,
      itemBuilder: (context, index) {
        final dayNumber = index - firstWeekday + 2;
        final isValidDay = dayNumber > 0 && dayNumber <= daysInMonth;

        if (!isValidDay) {
          return const SizedBox();
        }

        final date = DateTime(currentMonth.year, currentMonth.month, dayNumber);
        return _buildMobileDateCell(date, now);
      },
    );
  }

  Widget _buildMobileDateCell(DateTime date, DateTime now) {
    final isSelected =
        selectedDate != null &&
        date.year == selectedDate!.year &&
        date.month == selectedDate!.month &&
        date.day == selectedDate!.day;

    final isToday =
        date.year == now.year && date.month == now.month && date.day == now.day;

    final isInRange = isDateInSelectedRange(date);
    final isStartDate = isRangeStartDate(date);
    final isCustomRangeStart =
        customRangeStart != null &&
        date.year == customRangeStart!.year &&
        date.month == customRangeStart!.month &&
        date.day == customRangeStart!.day;
    final isCustomRangeEnd =
        customRangeEnd != null &&
        date.year == customRangeEnd!.year &&
        date.month == customRangeEnd!.month &&
        date.day == customRangeEnd!.day;

    Color? bgColor;
    Color textColor = AppTheme.black;

    if (isSelected || isStartDate || isCustomRangeStart || isCustomRangeEnd) {
      bgColor = AppTheme.selectedComboBoxBorder;
      textColor = AppTheme.white;
    } else if (isInRange) {
      bgColor = AppTheme.searchbarBg;
      textColor = AppTheme.black;
    } else if (isToday) {
      bgColor = AppTheme.selectedComboBoxBorder.withValues(alpha: 0.2);
      textColor = AppTheme.selectedComboBoxBorder;
    }

    return GestureDetector(
      onTap: () => onDateSelection(date),
      child: Container(
        margin: EdgeInsets.all(0.5), // Reduced margin for better fit
        decoration: BoxDecoration(
          color: bgColor,
          borderRadius: BorderRadius.circular(6), // Slightly smaller radius
          border: isToday && !isSelected && !isStartDate
              ? Border.all(color: AppTheme.selectedComboBoxBorder, width: 1)
              : null,
        ),
        child: Center(
          child: Text(
            date.day.toString(),
            style:
                AppFonts.regularTextStyle(
                  12, // Reduced font size by 2px (was 14)
                  color: textColor,
                ).copyWith(
                  fontWeight: isSelected || isToday || isStartDate
                      ? FontWeight.bold
                      : FontWeight.normal,
                ),
          ),
        ),
      ),
    );
  }

  Widget _buildMobileActionButtons() {
    return Padding(
      padding: EdgeInsets.only(
        top: defaultPadding * 0.8, // More spacing from calendar
        left: defaultPadding * 0.3,
        right: defaultPadding * 0.3,
        bottom: defaultPadding * 0.6,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Full width selected date display button
          _buildMobileSelectedDisplayButton(),
          SizedBox(
            height: defaultPadding * 0.8,
          ), // More spacing between sections
          // Cancel and Apply buttons in a row
          Row(
            children: [
              Expanded(child: _buildMobileCancelButton()),
              SizedBox(width: defaultPadding * 0.6),
              Expanded(child: _buildMobileApplyButton()),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMobileSelectedDisplayButton() {
    String displayText = _getDisplayText();

    return Container(
      width: double.infinity, // Ensure full width
      padding: EdgeInsets.symmetric(
        horizontal: defaultPadding * 0.8,
        vertical: defaultPadding * 0.6,
      ),
      decoration: BoxDecoration(
        color: AppTheme.searchbarBg.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppTheme.comboBoxBorder.withValues(alpha: 0.5),
        ),
      ),
      child: Text(
        displayText,
        style: AppFonts.regularTextStyle(
          13, // Slightly larger font
          color: AppTheme.tableDataFont,
        ),
        textAlign: TextAlign.center,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildMobileCalanderButton({
    required String text,
    required VoidCallback onTap,
    required Color backgroundColor,
    required Color textColor,
    Border? border,
    bool isActive = true, // Add isActive parameter with default true
  }) {
    return GestureDetector(
      onTap: isActive ? onTap : null, // Only allow tap when active
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: defaultPadding * 0.6,
          vertical: defaultPadding * 0.5,
        ),
        decoration: BoxDecoration(
          color: isActive
              ? backgroundColor
              : backgroundColor.withValues(
                  alpha: 0.5,
                ), // Apply opacity when inactive
          borderRadius: BorderRadius.circular(8),
          border: border,
        ),
        child: Center(
          child: Text(
            text,
            style: AppFonts.regularTextStyle(
              13,
              color: isActive
                  ? textColor
                  : textColor.withValues(
                      alpha: 0.7,
                    ), // Dimmed text when inactive
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMobileCancelButton() {
    return _buildMobileCalanderButton(
      text: cancel,
      onTap: onCancel,
      backgroundColor: AppTheme.searchbarBg,
      textColor: AppTheme.black,
      border: Border.all(color: AppTheme.comboBoxBorder),
    );
  }

  Widget _buildMobileApplyButton() {
    final bool isActive = _hasUserInteracted();
    return _buildMobileCalanderButton(
      text: applyLabel,
      onTap: onApply,
      backgroundColor: AppTheme.selectedComboBoxBorder,
      textColor: AppTheme.white,
      isActive: isActive, // Pass the active state
    );
  }
}
