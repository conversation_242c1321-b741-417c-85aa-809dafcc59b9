import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:meta/meta.dart';

import '/src/core/enum/user_role.dart';
import '/src/domain/models/info_card.dart';
import '/src/domain/repository/get_infocard_repository.dart';
import '/src/presentation/cubit/infocard/cubit/infocard_state.dart';
import '../../../../core/config/constants.dart';
import '../../../../core/network/api_config.dart';
import '/src/domain/models/info_card_item_model.dart';

class DashboardCubit extends Cubit<DashboardCubitState> {
  DashboardCubit(this._infoCardRepository) : super(DashboardInitial());

  final GetInfocardRepository _infoCardRepository;
  final String type = "TOTAL";
  // final String type = "LAST_MONTH";

  Future<void> fetchAllDashboardInfoCardsitems(UserRole role) async {
    try {
      emit(DashboardCardsLoading());

      List<InfoCardData> infoCards = [];

      Future<InfoCardItemModel?> safeApiCall(
        Future<InfoCardItemModel> apiCall,
      ) async {
        try {
          return await apiCall;
        } catch (e) {
          print("API Call Failed: $e");
          return null; // Continue flow even if fails
        }
      }

      switch (role) {
        case UserRole.admin:
          {
            final totalBrokeragesFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalBrokerages,
                type,
              ),
            );
            final totalAgentsFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalAgents,
                type,
              ),
            );
            final totalSalesFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalSales,
                type,
              ),
            );
            final totalRevenueFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalRevenue,
                type,
              ),
            );
            final lastMonthBrokeragesFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalBrokerages,
                "LAST_MONTH",
              ),
            );
            final lastMonthAgentsFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalAgents,
                "LAST_MONTH",
              ),
            );

            final results = await Future.wait([
              totalBrokeragesFuture,
              totalAgentsFuture,
              totalSalesFuture,
              totalRevenueFuture,
              lastMonthBrokeragesFuture,
              lastMonthAgentsFuture,
            ]);

            final totalBrokerages = results[0];
            final totalAgents = results[1];
            final totalSales = results[2];
            final totalRevenue = results[3];
            final lastMonthBrokerages = results[4];
            final lastMonthAgents = results[5];

            infoCards = [
              InfoCardData(
                title: 'Total Brokerages',
                value: totalBrokerages != null
                    ? totalBrokerages.data.value.toString()
                    : 'N/A',
                assetImage: '$iconAssetpath/brokers.png',
                iconColor: Colors.blue,
                subtitle: "Last Month",
                additionalInfo: lastMonthBrokerages != null
                    ? lastMonthBrokerages.data.value.toString()
                    : 'N/A',
              ),
              InfoCardData(
                title: 'Total Agents',
                value: totalAgents != null
                    ? totalAgents.data.value.toString()
                    : 'N/A',
                assetImage: '$iconAssetpath/agents.png',
                iconColor: Colors.blue,
                subtitle: "Last Month",
                additionalInfo: lastMonthAgents != null
                    ? lastMonthAgents.data.value.toString()
                    : 'N/A',
              ),
              InfoCardData(
                title: 'Total Sales',
                value: totalSales != null
                    ? totalSales.data.value.toString()
                    : 'N/A',
                assetImage: '$iconAssetpath/sales.png',
                iconColor: Colors.blue,
                subtitle: "Year",
                additionalInfo: totalSales != null
                    ? totalSales.data.year.toString()
                    : 'N/A',
              ),
              InfoCardData(
                title: 'Total Revenue',
                value: totalRevenue != null
                    ? formatCurrency(totalRevenue.data.value)
                    : 'N/A',
                assetImage: '$iconAssetpath/revenue.png',
                iconColor: Colors.blue,
                subtitle: "Year",
                additionalInfo: totalRevenue != null
                    ? totalRevenue.data.year.toString()
                    : 'N/A',
              ),
            ];
            break;
          }
        case UserRole.platformOwner:
          {
            final totalBrokeragesFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalBrokerages,
                type,
              ),
            );
            final totalAgentsFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalAgents,
                type,
              ),
            );
            final totalSalesFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalSales,
                type,
              ),
            );
            final totalRevenueFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalRevenue,
                type,
              ),
            );
            final lastMonthBrokeragesFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalBrokerages,
                "LAST_MONTH",
              ),
            );
            final lastMonthAgentsFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalAgents,
                "LAST_MONTH",
              ),
            );

            final results = await Future.wait([
              totalBrokeragesFuture,
              totalAgentsFuture,
              totalSalesFuture,
              totalRevenueFuture,
              lastMonthBrokeragesFuture,
              lastMonthAgentsFuture,
            ]);

            final totalBrokerages = results[0];
            final totalAgents = results[1];
            final totalSales = results[2];
            final totalRevenue = results[3];
            final lastMonthBrokerages = results[4];
            final lastMonthAgents = results[5];

            infoCards = [
              InfoCardData(
                title: 'Total Brokerages',
                value: totalBrokerages != null
                    ? totalBrokerages.data.value.toString()
                    : 'N/A',
                assetImage: '$iconAssetpath/brokers.png',
                iconColor: Colors.blue,
                subtitle: "Last Month",
                additionalInfo: lastMonthBrokerages != null
                    ? lastMonthBrokerages.data.value.toString()
                    : 'N/A',
              ),
              InfoCardData(
                title: 'Total Agents',
                value: totalAgents != null
                    ? totalAgents.data.value.toString()
                    : 'N/A',
                assetImage: '$iconAssetpath/agents.png',
                iconColor: Colors.blue,
                subtitle: "Last Month",
                additionalInfo: lastMonthAgents != null
                    ? lastMonthAgents.data.value.toString()
                    : 'N/A',
              ),
              InfoCardData(
                title: 'Total Sales',
                value: totalSales != null
                    ? totalSales.data.value.toString()
                    : 'N/A',
                assetImage: '$iconAssetpath/sales.png',
                iconColor: Colors.blue,
                subtitle: "Year",
                additionalInfo: totalSales != null
                    ? totalSales.data.year.toString()
                    : 'N/A',
              ),
              InfoCardData(
                title: 'Total Revenue',
                value: totalRevenue != null
                    ? formatCurrency(totalRevenue.data.value)
                    : 'N/A',
                assetImage: '$iconAssetpath/revenue.png',
                iconColor: Colors.blue,
                subtitle: "Year",
                additionalInfo: totalRevenue != null
                    ? totalRevenue.data.year.toString()
                    : 'N/A',
              ),
            ];
            break;
          }

        case UserRole.brokerage:
          {
            final totalAgentsFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalAgents,
                type,
              ),
            );

            final totalRevenueFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalRevenue,
                type,
              ),
            );
            final totalCommissionsFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalCommissions,
                type,
              ),
            );
            final totalSalesFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalSales,
                type,
              ),
            );

            final lastMonthAgentsFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalAgents,
                "LAST_MONTH",
              ),
            );

            final results = await Future.wait([
              totalAgentsFuture,
              totalCommissionsFuture,
              lastMonthAgentsFuture,
              totalRevenueFuture,
              totalSalesFuture,
            ]);

            final totalAgents = results[0];
            final totalCommissions = results[1];
            final lastMonthAgents = results[2];
            final totalRevenue = results[3];
            final totalSales = results[4];

            infoCards = [
              InfoCardData(
                title: 'Total Sales',
                value: totalSales != null
                    ? totalSales.data.value.toString()
                    : 'N/A',
                assetImage: '$iconAssetpath/sold_home.png',
                iconColor: Colors.blue,
                subtitle: "Year",
                additionalInfo: totalSales != null
                    ? totalSales.data.year.toString()
                    : 'N/A',
              ),
              InfoCardData(
                title: 'Total Agents',
                value: totalAgents != null
                    ? formatNumber(totalAgents.data.value)
                    : 'N/A',
                assetImage: '$iconAssetpath/agents.png',
                iconColor: Colors.blue,
                subtitle: "Last Month",
                additionalInfo: lastMonthAgents != null
                    ? lastMonthAgents.data.value.toString()
                    : 'N/A',
              ),
              InfoCardData(
                title: 'Total Revenue',
                value: totalRevenue != null
                    ? formatCurrency(totalRevenue.data.value)
                    : 'N/A',
                assetImage: '$iconAssetpath/revenue.png',
                iconColor: Colors.blue,
                subtitle: "Year",
                additionalInfo: totalRevenue != null
                    ? totalRevenue.data.year.toString()
                    : 'N/A',
              ),
              InfoCardData(
                title: 'Total Commissions',
                value: totalCommissions != null
                    ? formatCurrency(totalCommissions.data.value)
                    : 'N/A',
                assetImage: '$iconAssetpath/revenue.png',
                iconColor: Colors.blue,
                subtitle: "Year",
                additionalInfo: totalCommissions != null
                    ? totalCommissions.data.year.toString()
                    : 'N/A',
              ),
            ];
            break;
          }

        case UserRole.agent:
          {
            final totalAgentsFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalAgents,
                type,
              ),
            );

            final totalSalesFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalSales,
                type,
              ),
            );

            final totalCommissionsFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalCommissions,
                type,
              ),
            );
            final lastMonthAgentsFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalAgents,
                "LAST_MONTH",
              ),
            );

            final personalCommissionsFuture = safeApiCall(
              _infoCardRepository.getInfocardItemValue(
                APIConfig.totalCommissions,
                "PERSONAL",
              ),
            );

            final results = await Future.wait([
              totalAgentsFuture,
              totalCommissionsFuture,
              lastMonthAgentsFuture,
              personalCommissionsFuture,
              totalSalesFuture,
            ]);

            final totalAgents = results[0];
            final totalCommissions = results[1];
            final lastMonthAgents = results[2];
            final personalCommissions = results[3];
            final totalSales = results[4];

            infoCards = [
              InfoCardData(
                title: 'Total Agents',
                value: totalAgents != null
                    ? formatNumber(totalAgents.data.value)
                    : 'N/A',
                assetImage: '$iconAssetpath/agents.png',
                iconColor: Colors.blue,
                subtitle: "Last Month",
                additionalInfo: lastMonthAgents != null
                    ? lastMonthAgents.data.value.toString()
                    : 'N/A',
              ),
              InfoCardData(
                title: 'Total Sales',
                value: totalSales != null
                    ? totalSales.data.value.toString()
                    : 'N/A',
                assetImage: '$iconAssetpath/sold_home.png',
                iconColor: Colors.blue,
                subtitle: "Year",
                additionalInfo: totalSales != null
                    ? totalSales.data.year.toString()
                    : 'N/A',
              ),
              InfoCardData(
                title: 'Personal Commissions',
                value: personalCommissions != null
                    ? formatCurrency(personalCommissions.data.value)
                    : 'N/A',
                assetImage: '$iconAssetpath/revenue.png',
                iconColor: Colors.blue,
                subtitle: "Year",
                additionalInfo: personalCommissions != null
                    ? personalCommissions.data.year.toString()
                    : 'N/A',
              ),
              InfoCardData(
                title: 'Total Commissions',
                value: totalCommissions != null
                    ? formatCurrency(totalCommissions.data.value)
                    : 'N/A',
                assetImage: '$iconAssetpath/revenue.png',
                iconColor: Colors.blue,
                subtitle: "Year",
                additionalInfo: totalCommissions != null
                    ? totalCommissions.data.year.toString()
                    : 'N/A',
              ),
            ];
            break;
          }
        default:
          {
            infoCards = [];
            break;
          }
      }

      emit(DashboardCardsSuccess(infoCards));
    } catch (e) {
      emit(DashboardCardsFailure(e.toString()));
    }
  }

  String formatCurrency(int value) {
    return '\$${(value / 1000).toStringAsFixed(0)}K';
  }

  String formatNumber(int value) {
    return value >= 1000
        ? '${(value / 1000).toStringAsFixed(0)}K'
        : value.toString();
  }
}
