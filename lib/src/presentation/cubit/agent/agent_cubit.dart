import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import '../../../core/services/exceptions.dart';
import '../../../domain/models/agent_model.dart';
import '../../../domain/repository/agent_repository.dart';

part 'agent_state.dart';

class AgentCubit extends Cubit<AgentState> {
  final AgentRepository _agentRepository;

  AgentCubit(this._agentRepository) : super(AgentInitial());

  /// Get list of agents with POST request
  Future<void> getAgents({
    int page = 0,
    int size = 20,
    String sortBy = "id",
    String sortDirection = "ASC",
    String searchString = "",
    DateTime? joiningDate,
    required String userId,
    bool loadMore = false,
  }) async {
    if (!loadMore) {
      emit(AgentLoading());
    }

    try {
      // Build request body in cubit
      final Map<String, dynamic> requestBody = {
        'page': page,
        'size': size,
        'sortBy': sortBy,
        'sortDirection': sortDirection,
        'searchString': searchString,
        'joiningDate': joiningDate?.toIso8601String(),
        'userId': userId,
      };

      final response = await _agentRepository.getAgents(requestBody);

      emit(
        AgentLoaded(
          agents: response.content,
          totalCount: response.totalElements,
          currentPage: response.number,
          hasMore: !response.last,
        ),
      );
    } on ApiException catch (e) {
      emit(AgentError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(
        AgentError(message: 'An unexpected error occurred: ${e.toString()}'),
      );
    }
  }

  /// Get agent details by ID
  Future<void> getAgentById(String agentId) async {
    emit(AgentLoading());

    try {
      final agent = await _agentRepository.getAgentById(agentId);
      emit(
        AgentLoaded(
          agents: [agent],
          totalCount: 1,
          currentPage: 0,
          hasMore: false,
        ),
      );
    } on ApiException catch (e) {
      emit(AgentError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(
        AgentError(message: 'An unexpected error occurred: ${e.toString()}'),
      );
    }
  }

  /// Clear current state
  void clearAgents() {
    emit(AgentInitial());
  }

  /// Create a new agent
  Future<dynamic> createAgentnew(Map<String, dynamic> requestBody) async {
    print('requestBody-----$requestBody');
    try {
      final response = await _agentRepository.createAgent(requestBody);
      print('response data sending-----$response');
      if (response != false) {
        // Extract userid from response data
        String? userId;
        print(response);
        print(response['status']);
        print(response['message']);
        print(response['data']);
        final data = response['data'];
        print(data['userID']);
        userId = data['userID'];
        emit(
          AgentCreated(
            userId: userId,
            responseData: response is Map<String, dynamic> ? response : null,
          ),
        );
      } else {
        print('agent creation failed----');
        emit(AgentError(message: 'Failed to create agent'));
      }
    } on ApiException catch (e) {
      print('data send error $e.message');
      print('data send error $e');
      print(e.statusCode);
      emit(AgentError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      print('data send error catch ${e.toString()}');
      emit(
        AgentError(message: 'An unexpected error occurred: ${e.toString()}'),
      );
    }
  }

  /// Upload agent file
  Future<void> uploadAgentFile(Map<String, dynamic> requestBody) async {
    try {
      final success = await _agentRepository.uploadAgentFile(requestBody);
      if (success) {
        emit(AgentFileUploaded());
      } else {
        emit(AgentError(message: 'Failed to upload agent file'));
      }
    } on ApiException catch (e) {
      emit(AgentError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(
        AgentError(message: 'An unexpected error occurred: ${e.toString()}'),
      );
    }
  }
}
