part of 'top_performers_cubit.dart';

abstract class TopPerformersState {
  final List<TopPerformers>? agentTopPerformers;
  final List<TopPerformers>? brokerageTopPerformers;

  TopPerformersState({
    this.agentTopPerformers,
    this.brokerageTopPerformers,
  });
}

final class TopPerformersInitial extends TopPerformersState {}

final class TopPerformersLoading extends TopPerformersState {}

final class TopPerformersLoaded extends TopPerformersState {
  TopPerformersLoaded({
    super.agentTopPerformers,
    super.brokerageTopPerformers,
  });

  List<Object?> get props => [ agentTopPerformers, brokerageTopPerformers];
}

final class TopPerformersError extends TopPerformersState {
  final String message;
  final int? statusCode;

  TopPerformersError({required this.message, this.statusCode});

  List<Object?> get props => [message, statusCode];
}
