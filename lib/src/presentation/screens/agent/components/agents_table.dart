import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:neorevv/src/core/config/app_strings.dart' as AppRoutes;
import '../../../../core/config/responsive.dart';
import '/src/presentation/shared/components/tables/action_button_eye.dart';
import '/src/core/config/app_strings.dart';
import '/src/core/config/constants.dart';
import '/src/core/theme/app_fonts.dart';
import '/src/core/theme/app_theme.dart';
import '../../../../domain/models/agent_model.dart';
import '/src/presentation/cubit/agent/agent_cubit.dart';
import '/src/presentation/cubit/user/user_cubit.dart';
import '/src/core/services/locator.dart';

import '/src/presentation/shared/components/tables/CustomDataTableWidget.dart';

class AgentsTable extends HookWidget {
  final bool showEditOptions;
  final bool showRecruits;
  const AgentsTable({
    super.key,
    this.showEditOptions = false,
    this.showRecruits = false,
  });

  @override
  Widget build(BuildContext context) {
    // Get user from UserCubit
    final user = context.watch<UserCubit>().state.user;

    // Use predefined headers from app_strings.dart
    final List<String> formattedHeaders = showRecruits
        ? [
            agentName,
            agentRole,
            agentJoinDate,
            agentRefferedBy,
            agentTotalSales,
            agentCommission,
          ]
        : [
            agentName,
            agentLevel,
            agentJoinDate,
            agentTotalSales,
            agentEarning,
            agentState,
            agentCity,
          ];

    // Initialize sorting state
    final sortColumn = useState<String>('');
    final sortAscending = useState<bool>(true);

    // Check if user is available
    if (user == null) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.person_outline, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              userNotFound,
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(pleaseLoginToAccessAgentData),
          ],
        ),
      );
    }

    return BlocProvider(
      create: (context) {
        return AgentCubit(locator())..getAgents(
          userId: user.userId, // Use userId from UserCubit
          page: 0,
          size: 20,
        );
      },
      child: BlocBuilder<AgentCubit, AgentState>(
        builder: (context, state) {
          List<AgentModel> agentData = [];
          bool isLoading = false;
          String? errorMessage;

          if (state is AgentLoading) {
            isLoading = true;
          } else if (state is AgentLoaded) {
            agentData = state.agents;
          } else if (state is AgentError) {
            errorMessage = state.message;
          }

          // Helper function to sort agents
          List<AgentModel> getSortedAgents() {
            if (agentData.isEmpty) return <AgentModel>[];

            final sorted = List<AgentModel>.from(agentData);
            if (sortColumn.value.isNotEmpty) {
              sorted.sort((a, b) {
                dynamic aValue, bValue;

                switch (sortColumn.value) {
                  case agentName:
                    aValue = a.fullName;
                    bValue = b.fullName;
                    break;
                  case agentJoinDate:
                    aValue = a.joiningDate;
                    bValue = b.joiningDate;
                    break;
                  case agentState:
                    aValue = a.state;
                    bValue = b.state;
                    break;
                  case agentCity:
                    aValue = a.city;
                    bValue = b.city;
                    break;
                  case agentRole:
                    aValue = a.role;
                    bValue = b.role;
                    break;

                  case agentEarning:
                    aValue = a.commissionEarnings;
                    bValue = b.commissionEarnings;
                    break;
                  case agentRefferedBy:
                    aValue = a.associatedBroker;
                    bValue = b.associatedBroker;
                    break;
                  case agentTotalSales:
                    aValue = a.salesMade;
                    bValue = b.salesMade;
                    break;
                  case agentCommission:
                    aValue = a.commissionEarnings;
                    bValue = b.commissionEarnings;
                    break;
                  default:
                    aValue = '';
                    bValue = '';
                }

                final comparison = aValue is num && bValue is num
                    ? aValue.compareTo(bValue)
                    : aValue is DateTime && bValue is DateTime
                    ? aValue.compareTo(bValue)
                    : aValue.toString().compareTo(bValue.toString());
                return sortAscending.value ? comparison : -comparison;
              });
            }
            return sorted;
          }

          void handleSort(String columnName, bool ascending) {
            sortColumn.value = columnName;
            sortAscending.value = ascending;
          }

          final sortedAgents = getSortedAgents();

          if (isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    errorMessage.contains(unauthorizedStatus) ||
                            errorMessage.contains(unauthorizedText)
                        ? Icons.lock_outline
                        : Icons.error_outline,
                    size: 64,
                    color: Colors.red,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    errorMessage.contains(unauthorizedStatus) ||
                            errorMessage.contains(unauthorizedText)
                        ? authenticationRequired
                        : errorLoadingData,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    errorMessage.contains(unauthorizedStatus) ||
                            errorMessage.contains(unauthorizedText)
                        ? pleaseLoginToAccessAgentData
                        : '$errorPrefix$errorMessage',
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () async {
                      if (context.mounted) {
                        context.read<AgentCubit>().getAgents(
                          userId: user.userId, // Use dynamic userId
                          page: 0,
                          size: 20,
                        );
                      }
                    },
                    child: const Text(retry),
                  ),
                ],
              ),
            );
          }

          final isDesktop = Responsive.isDesktop(context);

          return LayoutBuilder(
            builder: (context, constraints) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  //Customized table layout
                  Flexible(
                    child: CustomDataTableWidget<AgentModel>(
                      data: sortedAgents,
                      title: agents,
                      titleIcon: "$iconAssetpath/user.png",
                      searchHint: searchAgent,
                      searchFn: (agent) =>
                          agent.fullName +
                          agent.phone +
                          agent.email +
                          agent.state +
                          agent.city +
                          agent.level +
                          agent.salesMade.toString() +
                          agent.commissionEarnings.toString(),
                      // Dynamic filtering system
                      filterColumnNames: [
                        //agentName, // name
                        //agentState, // state
                        //agentRole, // role
                        agentJoinDate, // join date
                      ],
                      filterValueExtractors: {
                        //agentName: (agent) => agent.fullName,
                        //agentState: (agent) => agent.state,
                        //agentRole: (agent) => agent.level,
                        agentJoinDate: (agent) =>
                            agent.joiningDate.toIso8601String(),
                      },
                      dateFilterColumns: const [
                        agentJoinDate, // Join date should use calendar picker
                      ],
                      columnNames: formattedHeaders,
                      cellBuilders: showRecruits
                          ? [
                              (agent) => agent.fullName,
                              (agent) => agent.level,
                              (agent) =>
                                  '${agent.joiningDate.day}/${agent.joiningDate.month}/${agent.joiningDate.year}',
                              (agent) => agent.associatedBroker, // referredBy
                              (agent) => agent.totalDownlineSales
                                  .toString(), // totalSales
                              (agent) =>
                                  '$currencySymbol${agent.commissionEarnings.toStringAsFixed(2)}', // commission
                            ]
                          : [
                              (agent) => agent.fullName,
                              (agent) => agent.level,
                              (agent) =>
                                  '${agent.joiningDate.day}/${agent.joiningDate.month}/${agent.joiningDate.year}',
                              (agent) =>
                                  agent.salesMade.toString(), // totalSales
                              (agent) =>
                                  '$currencySymbol${agent.commissionEarnings.toStringAsFixed(2)}', // earning
                              (agent) => agent.state,
                              (agent) => agent.city,
                            ],

                      /// to show icons before the cell content. eg: username and user icon
                      iconCellBuilders: [
                        (agent) => TableCellData(
                          text: agent.fullName,
                          leftIconAsset: "$iconAssetpath/agent_round.png",
                          iconSize: 28,
                        ),
                        null, // level - no icon
                        null, // joinDate - no icon
                        showRecruits
                            ? (agent) => TableCellData(
                                text: agent.associatedBroker,
                                leftIconAsset: "$iconAssetpath/agent_round.png",
                                iconSize: 28,
                              )
                            : null, // referredBy broker name or totalSales
                        null, // totalSales/earning - no icon
                        null, // commission/state - no icon
                        if (!showRecruits)
                          null, // city - no icon (only for non-recruits)
                      ],

                      /// Boolean flags to indicate which columns use icon cell builders. can enable/disable by setting this flag
                      useIconBuilders: [
                        true, // name - use icon
                        false, // level - use text
                        false, // joinDate - use text
                        showRecruits, // referredBy broker name - use icon / totalSales - use text
                        false, // totalSales/earning - use text
                        false, // commission/state - use text
                        if (!showRecruits)
                          false, // city - use text (only for non-recruits)
                      ],

                      /// Widget builders for styled cells - none needed for this table
                      widgetCellBuilders: showRecruits
                          ? [
                              null, // name - use text
                              null, // level - use text
                              null, // joinDate - use text
                              null, // referredBy - use text
                              null, // totalSales - use text
                              null, // commission - use text
                            ]
                          : [
                              null, // name - use text
                              null, // level - use text
                              null, // joinDate - use text
                              null, // totalSales - use text
                              null, // earning - use text
                              null, // state - use text
                              null, // city - use text
                            ],
                      // Boolean flags to indicate which columns use widget builders
                      useWidgetBuilders: showRecruits
                          ? [
                              false, // name - use text
                              false, // level - use text
                              false, // joinDate - use text
                              false, // referredBy - use text
                              false, // totalSales - use text
                              false, // commission - use text
                            ]
                          : [
                              false, // name - use text
                              false, // level - use text
                              false, // joinDate - use text
                              false, // totalSales - use text
                              false, // earning - use text
                              false, // state - use text
                              false, // city - use text
                            ],
                      actionBuilders: [
                        (context, agent) => ActionButtonEye(
                          onPressed: () => _onAgentAction(context, agent),
                          isCompact: true,
                          isMobile: false,
                        ),
                        if (showEditOptions) ...[
                          (context, agent) => ActionButtonEye(
                            onPressed: () => _onAgentAction(context, agent),
                            isCompact: true,
                            isMobile: false,
                            padding: 8,
                            icon: '$iconAssetpath/table_edit.png',
                          ),
                          (context, agent) => ActionButtonEye(
                            onPressed: () => _onAgentAction(context, agent),
                            isCompact: true,
                            isMobile: false,
                            padding: 8,
                            icon: '$iconAssetpath/delete.png',
                          ),
                        ],
                      ],
                      mobileCardBuilder: (context, agent) =>
                          _buildMobileAgentCard(agent, context),
                      onSort: handleSort,
                      emptyStateMessage: noDataAvailable,
                      useMinHeight: isDesktop,
                      minHeight: constraints.maxHeight,
                    ),
                  ),
                ],
              );
            },
          );
        },
      ),
    );
  }

  void _onAgentAction(BuildContext context, AgentModel agent) {
    // Navigate to agent detail or show action
    print('Agent action clicked for: ${agent.fullName}');
    context.go(AppRoutes.registerAgent);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('$actionClickedFor ${agent.fullName}')),
    );
  }

  Widget _buildMobileAgentCard(AgentModel agent, BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(agent.fullName, style: TextStyle(fontWeight: FontWeight.bold)),
          const SizedBox(height: 8),
          Text('$agentContact: ${agent.phone}'),
          Text('$agentEmail: ${agent.email}'),
          Text('$agentState: ${agent.state}'),
          Text('$agentCity: ${agent.city}'),
          Text('$agentLevel: ${agent.level}'),
          Text('$agentTotalSales: ${agent.salesMade}'),
          Text(
            '$agentEarning: $currencySymbol${agent.commissionEarnings.toStringAsFixed(2)}',
          ),
          Text(
            '$agentJoinDate: ${agent.joiningDate.day}/${agent.joiningDate.month}/${agent.joiningDate.year}',
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: ActionButtonEye(
              onPressed: () => _onAgentAction(context, agent),
              isMobile: true,
            ),
          ),
        ],
      ),
    );
  }
}
