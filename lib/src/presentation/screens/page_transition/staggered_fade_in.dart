import 'package:flutter/material.dart';

import '../../../core/config/constants.dart';

/// Staggered fade-in animation for smooth sequential appearance
class StaggeredFadeIn extends StatefulWidget {
  final Widget child;
  final Duration delay;

  const StaggeredFadeIn({
    super.key,
    required this.child,
    this.delay = Duration.zero,
  });

  @override
  State<StaggeredFadeIn> createState() => StaggeredFadeInState();
}

class StaggeredFadeInState extends State<StaggeredFadeIn>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startAnimation();
  }

  void _setupAnimations() {
    _controller = AnimationController(duration: animationDuration, vsync: this);

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeOutCubic));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.02),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeOutQuart));
  }

  void _startAnimation() {
    Future.delayed(widget.delay, () {
      if (mounted) {
        _controller.forward();
      }
    });
  }

  @override
  void didUpdateWidget(StaggeredFadeIn oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Don't restart animation on rebuilds - only animate on initial mount
    // The child comparison is unreliable during rebuilds (e.g., window resize)
    print('StaggeredFadeIn didUpdateWidget - NOT restarting animation');
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: widget.child,
          ),
        );
      },
    );
  }
}
