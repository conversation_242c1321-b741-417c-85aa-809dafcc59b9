import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '/src/presentation/cubit/broker/broker_cubit.dart';
import '../../../../core/config/responsive.dart';
import '../../../../core/utils/date_formatter.dart';
import '../../../../domain/models/broker_api.dart';
import '../../../../domain/models/user.dart';
import '../../../cubit/user/user_cubit.dart';
import '../../../shared/components/tables/action_button_eye.dart';
import '/src/core/config/app_strings.dart';
import '/src/core/config/constants.dart';

import '/src/presentation/shared/components/tables/CustomDataTableWidget.dart';

class BrokersTable extends HookWidget {
  final bool showEditOptions;
  final Function(Brokers)? onNavigateToAgentNetwork;

  const BrokersTable({
    super.key,
    this.onNavigateToAgentNetwork,
    this.showEditOptions = false,
  });

  @override
  Widget build(BuildContext context) {
    final isDesktop = Responsive.isDesktop(context);
    final sortedBrokers = useState<List<Brokers>>([]);
    final pageCount = useState(0);
    final currentpage = useState(0);
    final ValueNotifier<String?> searchString = useState(null);
    final ValueNotifier<DateTime?> selectedDate = useState(null);

    final user = context.watch<UserCubit>().state.user;

    // Use predefined headers from app_strings.dart
    final List<String> formattedHeaders = [
      brokerListNameColumnHeader,
      brokerListContactColumnHeader,
      brokerListEmailColumnHeader,
      brokerListAddressColumnHeader,
      brokerListJoinDateColumnHeader,
      brokerListAgentsColumnHeader,
      brokerListTotalSalesColumnHeader,
    ];

    void handleSort(String columnName, bool ascending) {
      // TODO: API
    }

    useEffect(() {
      //create microtask async
      Future.microtask(() async {
        if (context.mounted) {
          // final BrokerCubit brokerCubit = context.read<BrokerCubit>();
          await _fetchBrokers(
            context,
            user,
            selectedDate: selectedDate.value,
            page: currentpage.value,
            searchString: searchString.value,
          );
        }
      });

      return null;
    }, []);

    return LayoutBuilder(
      builder: (context, constraints) {
        return BlocConsumer<BrokerCubit, BrokerState>(
          listener: (context, state) {
            // TODO: implement listener
          },
          builder: (context, state) {
            if (state is BrokerLoaded) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                pageCount.value = state.brokerApi?.totalPages ?? 0;
                sortedBrokers.value = state.brokerApi?.brokers ?? [];
              });
            }
            return ValueListenableBuilder(
              valueListenable: sortedBrokers,
              builder: (context, value, child) {
                return CustomDataTableWidget<Brokers>(
                  data: sortedBrokers.value,
                  title: brokersTab,
                  titleIcon: "$iconAssetpath/user.png",
                  searchHint: searchHint,
                  searchFn: (broker) =>
                      broker.fullName +
                      broker.phone +
                      broker.email +
                      broker.city +
                      ', ' +
                      broker.state +
                      AppDateFormatter.formatJoiningDate(broker.joiningDate) +
                      broker.totalDownlineAgents.toString() +
                      broker.salesMade.toString(),
                  dateFilterColumns: const [
                    brokerListJoinDateColumnHeader, // Join date should use calendar picker
                  ],
                  filterColumnNames: [brokerListJoinDateColumnHeader],
                  filterValueExtractors: {
                    brokerListJoinDateColumnHeader: (broker) =>
                        AppDateFormatter.formatDateMMddyyyy(broker.joiningDate),
                  },
                  columnNames: formattedHeaders,
                  cellBuilders: [
                    (broker) => broker.fullName,
                    (broker) => broker.phone,
                    (broker) => broker.email,
                    (broker) => '${broker.city}, ${broker.state}',
                    (broker) =>
                        AppDateFormatter.formatDateMMddyyyy(broker.joiningDate),
                    (broker) => broker.totalDownlineAgents.toString(),
                    (broker) => '₹${broker.salesMade.toStringAsFixed(2)}',
                  ],
                  iconCellBuilders: [
                    (broker) => TableCellData(
                      text: broker.fullName,
                      leftIconAsset: "$iconAssetpath/agent_round.png",
                      iconSize: 30,
                    ),
                    null, // contact - no icon
                    null, // email - no icon
                    null, // address - no icon
                    null, // joinDate - no icon
                    null, // agents - no icon
                    null, // totalSales - no icon
                  ],
                  useIconBuilders: [
                    true, // name - use icon
                    false, // contact - use text
                    false, // email - use text
                    false, // address - use text
                    false, // joinDate - use text
                    false, // agents - use text
                    false, // totalSales - use text
                  ],
                  actionBuilders: [
                    (context, broker) => ActionButtonEye(
                      onPressed: () => _onBrokerAction(context, broker),
                      isCompact: true,
                      isMobile: false,
                    ),
                    if (showEditOptions) ...[
                      (context, broker) => ActionButtonEye(
                        onPressed: () => _onBrokerAction(context, broker),
                        isCompact: true,
                        isMobile: false,
                        padding: 8,
                        icon: '$iconAssetpath/table_edit.png',
                      ),
                      (context, broker) => ActionButtonEye(
                        onPressed: () => _onBrokerAction(context, broker),
                        isCompact: true,
                        isMobile: false,
                        padding: 8,
                        icon: '$iconAssetpath/delete.png',
                      ),
                    ],
                  ],
                  mobileCardBuilder: (context, broker) =>
                      _buildMobileBrokerCard(broker, context),
                  onSort: handleSort,
                  emptyStateMessage: noDataAvailable,
                  useMinHeight: false, // Disable fixed min height
                  pageCount: pageCount.value,
                  isLoading: state is BrokerLoading,
                  onDateFilterChanged: (value) async {
                    selectedDate.value = value;
                    await _fetchBrokers(
                      context,
                      user,
                      selectedDate: value,
                      page: currentpage.value,
                      searchString: searchString.value,
                    );
                  },
                  handleTableSearch: (value) async {
                    searchString.value = value;
                    await _fetchBrokers(
                      context,
                      user,
                      selectedDate: selectedDate.value,
                      page: currentpage.value,
                      searchString: value,
                    );
                  },
                  handlePagination: (page) async {
                    currentpage.value = page;
                    await _fetchBrokers(
                      context,
                      user,
                      selectedDate: selectedDate.value,
                      searchString: searchString.value,
                      page: page,
                    );
                  },
                );
              },
            );
          },
        );
      },
    );
  }

  Future<void> _fetchBrokers(
    BuildContext context,
    User? user, {
    required DateTime? selectedDate,
    required int page,
    required String? searchString,
  }) async {
    String? formattedDate = selectedDate != null
        ? AppDateFormatter.formatDateMMddyyyy(selectedDate)
        : null;

    // TODO: update sortBy static value when server side updates
    final payload = {
      "page": page > 0 ? page - 1 : 0,
      "size": 10,
      "sortBy": "id",
      "sortDirection": "ASC",
      "searchString": searchString,
      "joiningDate": formattedDate,
      "userId": user?.userId,
    };
    await context.read<BrokerCubit>().fetchBrokers(payload);
  }

  void _onBrokerAction(BuildContext context, Brokers broker) {
    if (onNavigateToAgentNetwork != null) {
      // Find the corresponding Broker object from brokersListJson
      try {
        onNavigateToAgentNetwork!(broker);
      } catch (e) {
        // If no matching broker found, show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Broker not found: ${broker.fullName}')),
        );
      }
    } else {
      // Fallback to showing snackbar if callback is null
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Action clicked for ${broker.fullName}')),
      );
    }
  }

  Widget _buildMobileBrokerCard(Brokers broker, BuildContext context) {
    String joinDate = AppDateFormatter.formatDateMMddyyyy(broker.joiningDate);
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                broker.fullName,
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text('$brokerListNameColumnHeader: ${broker.fullName}'),
          Text('$brokerListContactColumnHeader: ${broker.phone}'),
          Text('$brokerListEmailColumnHeader: ${broker.email}'),
          Text(
            '$brokerListAddressColumnHeader: ${broker.city}, ${broker.state}',
          ),
          Text('$brokerListJoinDateColumnHeader: $joinDate'),

          Text('$brokerListAgentsColumnHeader: ${broker.totalDownlineAgents}'),
          Text(
            '$brokerListSalesColumnHeader: ₹${broker.salesMade.toStringAsFixed(2)}',
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: ActionButtonEye(
              onPressed: () => _onBrokerAction(context, broker),
              isMobile: true,
            ),
          ),
        ],
      ),
    );
  }
}
