import 'package:flutter/material.dart';
import '../../../../core/config/constants.dart';

import '../../../../core/theme/app_theme.dart';

class RoundIconBtn extends StatelessWidget {
  final String icon;
  final VoidCallback onPressed;
  final Color iconColor;
  final Color backgroundColor;
  final double iconSize;

  const RoundIconBtn({
    required this.icon,
    required this.onPressed,
    this.iconColor = AppTheme.roundIconColor,
    this.backgroundColor = AppTheme.roundIconBgColor,
    this.iconSize = 30,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: iconSize,
        height: iconSize,
        decoration: BoxDecoration(
          color: backgroundColor,
          shape: BoxShape.circle,
          image: DecorationImage(image: AssetImage('$iconAssetpath/$icon.png')),
        ),
      ),
    );
  }
}
